:host {
  display: block;
  font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Helvetica, Arial, sans-serif, "Apple Color Emoji", "Segoe UI Emoji", "Segoe UI Symbol";
  color: #333;
}

.calendar-container {
  max-width: 900px;
  margin: 2rem auto;
  padding: 1.5rem;
  border-radius: 10px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
  background-color: #ffffff;
}

// --- Header ---
.calendar-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1.5rem;
  user-select: none;

  .header-title {
    text-align: center;
    h2 {
      margin: 0;
      font-weight: 500;
      font-size: 1.5rem;
    }
  }

  .nav-button, .today-button {
    background: none;
    border: 1px solid #ddd;
    border-radius: 50%;
    width: 40px;
    height: 40px;
    cursor: pointer;
    font-size: 1.2rem;
    transition: background-color 0.2s, color 0.2s;
    &:hover {
      background-color: #f0f0f0;
    }
  }

  .today-button {
      border-radius: 20px;
      width: auto;
      padding: 0 1rem;
      font-size: 0.9rem;
      font-weight: 500;
      margin-top: 0.25rem;
      border-color: #007bff;
      color: #007bff;
      &:hover {
          background-color: #007bff;
          color: white;
      }
  }
}

// --- Grid ---
.calendar-grid {
  display: grid;
  grid-template-columns: repeat(7, 1fr);
  gap: 5px;

  .day-name {
    font-weight: 600;
    text-align: center;
    padding-bottom: 0.5rem;
    color: #888;
  }

  .day-cell {
    position: relative;
    display: flex;
    justify-content: center;
    align-items: center;
    height: 50px;
    border: 1px solid transparent;
    border-radius: 50%;
    cursor: pointer;
    transition: all 0.2s ease-in-out;

    &:not(.blank):hover {
      background-color: #e9ecef;
    }

    &.today {
      border-color: #007bff;
      color: #007bff;
      font-weight: bold;
    }

    &.selected {
      background-color: #007bff;
      color: white;
      font-weight: bold;
    }

    // Dot for appointments
    &.has-appointment::after {
        content: '';
        position: absolute;
        bottom: 8px;
        width: 6px;
        height: 6px;
        border-radius: 50%;
        background-color: #dc3545;
    }
    &.selected.has-appointment::after {
        background-color: white; // Make dot visible on selected background
    }
  }
}

// --- Appointment Section ---
.appointment-section {
  margin-top: 2rem;
  padding-top: 1.5rem;
  border-top: 1px solid #eee;

  h3 {
    margin-top: 0;
    font-weight: 500;
  }

  textarea {
    width: 100%;
    min-height: 100px;
    padding: 0.75rem;
    border: 1px solid #ccc;
    border-radius: 8px;
    font-size: 1rem;
    resize: vertical;
    box-sizing: border-box;
    margin-bottom: 1rem;
  }

  .button-group {
    display: flex;
    gap: 1rem;
  }

  .save-button, .delete-button {
    padding: 0.75rem 1.5rem;
    border: none;
    border-radius: 8px;
    font-size: 1rem;
    font-weight: 500;
    cursor: pointer;
    transition: background-color 0.2s, transform 0.1s;

    &:active {
        transform: scale(0.98);
    }
  }

  .save-button {
    background-color: #28a745;
    color: white;
    &:hover {
        background-color: #218838;
    }
  }
  .delete-button {
    background-color: #dc3545;
    color: white;
    &:hover {
        background-color: #c82333;
    }
  }
}

// --- Notification Alert ---
.alert-notification {
    position: fixed;
    top: 20px;
    right: 20px;
    padding: 1rem 1.5rem;
    border-radius: 8px;
    color: white;
    box-shadow: 0 2px 10px rgba(0,0,0,0.2);
    z-index: 1000;
    animation: fadeInOut 3s ease-in-out forwards;

    &.alert-success { background-color: #28a745; }
    &.alert-error { background-color: #dc3545; }
    &.alert-info { background-color: #17a2b8; }
}

@keyframes fadeInOut {
    0% { opacity: 0; transform: translateY(-20px); }
    15% { opacity: 1; transform: translateY(0); }
    85% { opacity: 1; transform: translateY(0); }
    100% { opacity: 0; transform: translateY(-20px); }
}
