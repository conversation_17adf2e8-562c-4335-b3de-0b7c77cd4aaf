
import { Component, OnInit } from '@angular/core';
import { CommonModule, DatePipe } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { Subject, Subscription } from 'rxjs';
import { take } from 'rxjs/operators';

// A simple in-component service for notifications
class NotificationService {
  private notificationSubject = new Subject<{ message: string; type: 'success' | 'error' | 'info' }>();
  public notification$ = this.notificationSubject.asObservable();

  show(message: string, type: 'success' | 'error' | 'info' = 'info') {
    this.notificationSubject.next({ message, type });
  }
}

@Component({
  selector: 'app-calendar',
  standalone: true,
  // Import necessary modules for the template
  imports: [CommonModule, FormsModule, DatePipe],
  templateUrl: './calendar.component.html',
  styleUrls: ['./calendar.component.scss'],
  // Provide the notification service locally to this component
  providers: [NotificationService],
})
export class CalendarComponent implements OnInit {
  // Date-related properties
  public displayDate: Date = new Date();
  public today: Date = new Date();
  public daysInMonth: Date[] = [];
  public blankDays: number[] = [];
  public selectedDate: Date | null = null;

  // Appointment-related properties
  public appointments = new Map<string, string>(); // Key: 'YYYY-MM-DD', Value: appointment text
  public appointmentText: string = '';

  // Notification properties
  public notification: { message: string, type: string } | null = null;
  private notificationSubscription!: Subscription;

  constructor(private notificationService: NotificationService) {
     // Pre-populate with some example appointments
     const tomorrow = new Date();
     tomorrow.setDate(tomorrow.getDate() + 1);
     this.appointments.set(this.getDateKey(this.today), 'Stand-up meeting at 10 AM.');
     this.appointments.set(this.getDateKey(tomorrow), 'Deploy new feature to production.');
  }

  ngOnInit(): void {
    this.generateCalendar();
    this.setupNotificationListener();
  }

  ngOnDestroy(): void {
    if (this.notificationSubscription) {
      this.notificationSubscription.unsubscribe();
    }
  }

  /**
   * Listens for notifications from the service and displays them.
   */
  private setupNotificationListener(): void {
    this.notificationSubscription = this.notificationService.notification$.subscribe(notif => {
      this.notification = notif;
      // Auto-hide notification after 3 seconds
      setTimeout(() => this.notification = null, 3000);
    });
  }

  /**
   * Generates the days for the current display month.
   */
  public generateCalendar(): void {
    this.daysInMonth = [];
    this.blankDays = [];

    const year = this.displayDate.getFullYear();
    const month = this.displayDate.getMonth();

    const firstDayOfMonth = new Date(year, month, 1);
    const daysInMonthCount = new Date(year, month + 1, 0).getDate();

    // Create blank days for the first week
    const startingDay = firstDayOfMonth.getDay(); // 0 (Sun) to 6 (Sat)
    this.blankDays = Array(startingDay).fill(0);

    // Create date objects for each day of the month
    for (let i = 1; i <= daysInMonthCount; i++) {
      this.daysInMonth.push(new Date(year, month, i));
    }
  }

  /**
   * Navigates to the previous month.
   */
  public previousMonth(): void {
    this.displayDate.setMonth(this.displayDate.getMonth() - 1);
    this.generateCalendar();
  }

  /**
   * Navigates to the next month.
   */
  public nextMonth(): void {
    this.displayDate.setMonth(this.displayDate.getMonth() + 1);
    this.generateCalendar();
  }

  /**
   * Jumps back to the current month.
   */
  public goToToday(): void {
    this.displayDate = new Date();
    this.generateCalendar();
  }

  /**
   * Handles the click event on a day cell.
   * @param day The date object for the clicked day.
   */
  public selectDate(day: Date): void {
    this.selectedDate = day;
    const dateKey = this.getDateKey(day);
    this.appointmentText = this.appointments.get(dateKey) || '';
  }

  /**
   * Adds or updates an appointment for the selected date.
   */
  public saveAppointment(): void {
    if (!this.selectedDate) {
      this.notificationService.show('Please select a date first.', 'error');
      return;
    }
    if (!this.appointmentText.trim()) {
        this.deleteAppointment(); // If text is empty, treat it as a deletion
        return;
    }

    const dateKey = this.getDateKey(this.selectedDate);
    this.appointments.set(dateKey, this.appointmentText);
    this.notificationService.show('Appointment saved successfully!', 'success');
  }

  /**
   * Deletes the appointment for the selected date.
   */
  public deleteAppointment(): void {
    if (!this.selectedDate) return;

    const dateKey = this.getDateKey(this.selectedDate);
    if (this.appointments.has(dateKey)) {
        this.appointments.delete(dateKey);
        this.appointmentText = '';
        this.notificationService.show('Appointment deleted.', 'info');
    }
  }

  // --- Helper Functions for Template ---

  /**
   * Generates a consistent key for the appointments map from a Date object.
   * @param date The date to convert.
   * @returns A string in 'YYYY-MM-DD' format.
   */
  private getDateKey(date: Date): string {
    return date.toISOString().split('T')[0];
  }

  public isToday(date: Date): boolean {
    return this.getDateKey(date) === this.getDateKey(this.today);
  }

  public isSelected(date: Date): boolean {
    if (!this.selectedDate) return false;
    return this.getDateKey(date) === this.getDateKey(this.selectedDate);
  }

  public hasAppointment(date: Date): boolean {
    return this.appointments.has(this.getDateKey(date));
  }
}
