const express = require('express');
const cors = require('cors');
const bodyParser = require('body-parser');
const { Pool } = require('pg');
const bcrypt = require('bcryptjs');
const jwt = require('jsonwebtoken');
const app = express();
const port = 3000;

const pool = new Pool({
  user: 'postgres',
  host: 'localhost',
  database: 'payments',
  password: 'YOUR_PASS',
  port: 5432
});

app.use(cors());
app.use(bodyParser.json());

const SECRET_KEY = 'secret123';

function authMiddleware(req, res, next) {
  const token = req.headers['authorization'];
  if (!token) return res.status(401).send('No token provided');

  jwt.verify(token, SECRET_KEY, (err, decoded) => {
    if (err) return res.status(401).send('Invalid token');
    req.userId = decoded.id;
    next();
  });
}

app.post('/api/auth/register', async (req, res) => {
  const { email, password } = req.body;
  const hash = await bcrypt.hash(password, 10);
  try {
    await pool.query('INSERT INTO users (email, password) VALUES ($1, $2)', [email, hash]);
    res.send('User registered');
  } catch (e) {
    res.status(500).send('Registration error');
  }
});

app.post('/api/auth/login', async (req, res) => {
  const { email, password } = req.body;
  try {
    const result = await pool.query('SELECT * FROM users WHERE email = $1', [email]);
    const user = result.rows[0];
    if (!user || !(await bcrypt.compare(password, user.password))) {
      return res.status(401).send('Invalid credentials');
    }
    const token = jwt.sign({ id: user.id }, SECRET_KEY, { expiresIn: '1h' });
    res.json({ token });
  } catch (e) {
    res.status(500).send('Login error');
  }
});

app.post('/api/payment', authMiddleware, async (req, res) => {
  const { cardNumber, expiryDate, cvv, billingAddress } = req.body;
  try {
    const result = await pool.query(
      `INSERT INTO payments (card_number, expiry_date, cvv, billing_address, user_id)
       VALUES ($1,$2,$3,$4,$5) RETURNING id`,
      [cardNumber, expiryDate, cvv, billingAddress, req.userId]
    );
    res.json({ id: result.rows[0].id });
  } catch (e) {
    console.error(e);
    res.status(500).send('DB error');
  }
});

app.listen(port, () => console.log(`Backend running at http://localhost:${port}`));