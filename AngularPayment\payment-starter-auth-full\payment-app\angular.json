{"$schema": "./node_modules/@angular/cli/lib/config/schema.json", "version": 1, "defaultProject": "payment-app", "projects": {"payment-app": {"projectType": "application", "schematics": {}, "root": "", "sourceRoot": "src", "prefix": "app", "architect": {"build": {"options": {"outputPath": "dist/payment-app", "index": "src/index.html", "main": "src/main.ts", "polyfills": "src/polyfills.ts", "tsConfig": "tsconfig.app.json", "assets": ["src/assets"], "styles": ["src/styles.css"], "scripts": []}}}}}}