{"name": "payment-app", "version": "0.0.1", "scripts": {"ng": "ng", "start": "ng serve", "build": "ng build", "test": "ng test", "lint": "ng lint"}, "private": true, "dependencies": {"@angular/animations": "^16.0.0", "@angular/common": "^16.0.0", "@angular/compiler": "^16.0.0", "@angular/core": "^16.0.0", "@angular/forms": "^16.0.0", "@angular/platform-browser": "^16.0.0", "@angular/platform-browser-dynamic": "^16.0.0", "@angular/router": "^16.0.0", "@angular/material": "^16.0.0", "rxjs": "~7.8.0", "tslib": "^2.3.0", "zone.js": "~0.12.0"}, "devDependencies": {"@angular-devkit/build-angular": "^16.0.0", "@angular/cli": "~16.0.0", "@angular/compiler-cli": "^16.0.0", "typescript": "~5.1.0"}}