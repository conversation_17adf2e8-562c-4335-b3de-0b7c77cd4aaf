#!/bin/bash
# Exit immediately if a command exits with a non-zero status.
set -e
#Project name
PROJECT_NAME="fullcalendarP-app"

# Step 1: Create a New Angular Project
echo "Step 1: Creating a new Angular project..."
# Add --skip-git to avoid potential conflicts during script execution if you handle git separately
# ng new "$PROJECT_NAME" --standalone --defaults --skip-git
ng new "$PROJECT_NAME" --standalone --defaults
cd "$PROJECT_NAME"

# Step 2: Install FullCalendar Packages
echo "Step 2: Installing FullCalendar packages..."
npm install --save @fullcalendar/core @fullcalendar/daygrid @fullcalendar/interaction @fullcalendar/list @fullcalendar/angular

# Step 3: Create the FullCalendar Component
echo "Step 3: Creating the FullCalendar component..."
# Use --flat if you don't want a separate directory for the component
# ng generate component full-calendar --flat
ng generate component full-calendar

# Step 4: Implement the FullCalendar Component
echo "Step 4: Implementing the FullCalendar component..."

FULL_CALENDAR_COMPONENT_TS="src/app/full-calendar/full-calendar.component.ts"
FULL_CALENDAR_COMPONENT_HTML="src/app/full-calendar/full-calendar.component.html"
APP_COMPONENT_TS="src/app/app.component.ts"
APP_COMPONENT_HTML="src/app/app.component.html"

# --- full-calendar.component.ts ---
echo "Writing to $FULL_CALENDAR_COMPONENT_TS"
cat > "$FULL_CALENDAR_COMPONENT_TS" <<EOL
import { Component, OnInit } from '@angular/core';
import { CalendarOptions } from '@fullcalendar/core';
import dayGridPlugin from '@fullcalendar/daygrid';
import interactionPlugin from '@fullcalendar/interaction';
import listPlugin from '@fullcalendar/list';
import { FullCalendarModule } from '@fullcalendar/angular';

@Component({
  selector: 'app-full-calendar',
  standalone: true,
  imports: [FullCalendarModule],
  templateUrl: './full-calendar.component.html',
  styleUrls: ['./full-calendar.component.css']
})
export class FullCalendarComponent implements OnInit {
  calendarOptions: CalendarOptions = {
    plugins: [dayGridPlugin, interactionPlugin, listPlugin],
    headerToolbar: {
      left: 'prev,next today',
      center: 'title',
      right: 'dayGridMonth,listWeek'
    },
    initialView: 'dayGridMonth',
    weekends: true,
    editable: true,
    selectable: true,
    selectMirror: true,
    dayMaxEvents: true,
    events: [
      { title: 'Event 1', date: '2024-07-01' },
      { title: 'Event 2', date: '2024-07-03' }
    ],
    dateClick: this.handleDateClick.bind(this),
    eventClick: this.handleEventClick.bind(this)
  };

  ngOnInit(): void {
  }

  handleDateClick(arg: any) {
    alert('Date Clicked: ' + arg.dateStr);
  }

  handleEventClick(arg: any) {
    alert('Event Clicked: ' + arg.event.title);
  }
}
EOL

# --- full-calendar.component.html ---
echo "Writing to $FULL_CALENDAR_COMPONENT_HTML"
cat > "$FULL_CALENDAR_COMPONENT_HTML" <<EOL
<full-calendar [options]="calendarOptions"></full-calendar>
EOL

# --- app.component.ts ---
echo "Writing to $APP_COMPONENT_TS"
cat > "$APP_COMPONENT_TS" <<EOL
import { Component } from '@angular/core';
import { FullCalendarComponent } from './full-calendar/full-calendar.component';

@Component({
  selector: 'app-root',
  standalone: true,
  imports: [FullCalendarComponent],
  templateUrl: './app.component.html',
  styleUrl: './app.component.css'
})
export class AppComponent {
  title = 'fullcalendar-app';
}
EOL

# --- app.component.html ---
echo "Writing to $APP_COMPONENT_HTML"
cat > "$APP_COMPONENT_HTML" <<EOL
<app-full-calendar></app-full-calendar>
EOL

echo "Project setup complete."